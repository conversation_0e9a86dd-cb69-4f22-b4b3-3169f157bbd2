using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Data.Permissions
{
    /// <summary>
    /// Repository pattern wrapper for permission database operations
    /// Provides a clean interface for the GlobalPermissionService to interact with the database
    /// </summary>
    public static class PermissionRepository
    {
        #region Global Permissions

        /// <summary>
        /// Get global permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Global permission model or null if not found</returns>
        public static GlobalPermissionModel GetGlobalPermissions(int userId)
        {
            try
            {
                var globalPermission = PermissionDatabaseService.GetGlobalPermissions(userId);
                if (globalPermission == null) return null;

                // Convert from database model to service model
                return new GlobalPermissionModel
                {
                    UserId = globalPermission.UserId,
                    CanReadUsers = true, // Assuming read is always true for existing users
                    CanCreateUsers = globalPermission.CanCreateUsers,
                    CanEditUsers = globalPermission.CanEditUsers,
                    CanDeleteUsers = globalPermission.CanDeleteUsers,
                    CanPrintUsers = globalPermission.CanPrintUsers
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting global permissions from repository: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Update global permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissions">Global permissions to update</param>
        /// <returns>True if successful</returns>
        public static bool UpdateGlobalPermissions(int userId, GlobalPermissionModel permissions)
        {
            try
            {
                if (permissions == null) return false;

                // Convert from service model to database model
                var update = new GlobalPermissionUpdate
                {
                    UserId = userId,
                    CanCreateUsers = permissions.CanCreateUsers,
                    CanEditUsers = permissions.CanEditUsers,
                    CanDeleteUsers = permissions.CanDeleteUsers,
                    CanPrintUsers = permissions.CanPrintUsers
                };

                return PermissionDatabaseService.UpdateGlobalPermissions(update);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating global permissions in repository: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create default global permissions for a new user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful</returns>
        public static bool CreateDefaultGlobalPermissions(int userId)
        {
            try
            {
                var defaultPermissions = new GlobalPermissionModel
                {
                    UserId = userId,
                    CanReadUsers = true,  // Default to allow reading
                    CanCreateUsers = false,
                    CanEditUsers = false,
                    CanDeleteUsers = false,
                    CanPrintUsers = false
                };

                return UpdateGlobalPermissions(userId, defaultPermissions);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating default global permissions: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Role Permissions

        /// <summary>
        /// Get role permissions for a specific role and form
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>Role permission or null if not found</returns>
        public static RolePermission GetRolePermission(int roleId, string formName)
        {
            try
            {
                return PermissionDatabaseService.GetRolePermission(roleId, formName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permission: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get all role permissions for a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of role permissions</returns>
        public static List<RolePermission> GetRolePermissions(int roleId)
        {
            try
            {
                return PermissionDatabaseService.GetRolePermissions(roleId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permissions: {ex.Message}");
                return new List<RolePermission>();
            }
        }

        #endregion

        #region User Permissions

        /// <summary>
        /// Get user permission for a specific user and form
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>User permission or null if not found</returns>
        public static UserPermission GetUserPermission(int userId, string formName)
        {
            try
            {
                return PermissionDatabaseService.GetUserPermission(userId, formName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permission: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get all user permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user permissions</returns>
        public static List<UserPermission> GetUserPermissions(int userId)
        {
            try
            {
                return PermissionDatabaseService.GetUserPermissions(userId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permissions: {ex.Message}");
                return new List<UserPermission>();
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Check if user exists in the system
        /// </summary>
        /// <param name="userId">User ID to check</param>
        /// <returns>True if user exists</returns>
        public static bool UserExists(int userId)
        {
            try
            {
                return PermissionDatabaseService.UserExists(userId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking if user exists: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user's role ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Role ID or 0 if not found</returns>
        public static int GetUserRoleId(int userId)
        {
            try
            {
                return PermissionDatabaseService.GetUserRoleId(userId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user role ID: {ex.Message}");
                return 0;
            }
        }

        #endregion
    }
}
