﻿namespace ProManage.Forms.ReusableForms
{
    partial class MenuRibbon
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MenuRibbon));
            this.RibbonControl = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.BarButtonItemFirst = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemPrevious = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemNext = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemLast = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemNew = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemEdit = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemSave = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemCancel = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemDelete = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemAddRow = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemPrint = new DevExpress.XtraBars.BarButtonItem();
            this.tglStatus = new DevExpress.XtraBars.BarToggleSwitchItem();
            this.BarButtonPrint = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonPrintPreview = new DevExpress.XtraBars.BarButtonItem();
            this.RibbonPageEstimate = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.RibbonPageGroupOperations = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.RibbonPageGroupGrid = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.RibbonPageGroupNavigation = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl)).BeginInit();
            this.SuspendLayout();
            // 
            // RibbonControl
            // 
            this.RibbonControl.ExpandCollapseItem.Id = 0;
            this.RibbonControl.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.RibbonControl.ExpandCollapseItem,
            this.BarButtonItemFirst,
            this.BarButtonItemPrevious,
            this.BarButtonItemNext,
            this.BarButtonItemLast,
            this.BarButtonItemNew,
            this.BarButtonItemEdit,
            this.BarButtonItemSave,
            this.BarButtonItemCancel,
            this.BarButtonItemDelete,
            this.BarButtonItemAddRow,
            this.BarButtonItemPrint,
            this.tglStatus,
            this.BarButtonPrint,
            this.BarButtonPrintPreview});
            this.RibbonControl.Location = new System.Drawing.Point(0, 0);
            this.RibbonControl.MaxItemId = 17;
            this.RibbonControl.Name = "RibbonControl";
            this.RibbonControl.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.RibbonPageEstimate});
            this.RibbonControl.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2019;
            this.RibbonControl.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.True;
            this.RibbonControl.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.RibbonControl.Size = new System.Drawing.Size(1019, 131);
            this.RibbonControl.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
            // 
            // BarButtonItemFirst
            // 
            this.BarButtonItemFirst.Caption = "First";
            this.BarButtonItemFirst.Id = 1;
            this.BarButtonItemFirst.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemFirst.ImageOptions.SvgImage")));
            this.BarButtonItemFirst.Name = "BarButtonItemFirst";
            // 
            // BarButtonItemPrevious
            // 
            this.BarButtonItemPrevious.Caption = "Previous";
            this.BarButtonItemPrevious.Id = 2;
            this.BarButtonItemPrevious.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemPrevious.ImageOptions.SvgImage")));
            this.BarButtonItemPrevious.Name = "BarButtonItemPrevious";
            // 
            // BarButtonItemNext
            // 
            this.BarButtonItemNext.Caption = "Next";
            this.BarButtonItemNext.Id = 3;
            this.BarButtonItemNext.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemNext.ImageOptions.SvgImage")));
            this.BarButtonItemNext.Name = "BarButtonItemNext";
            // 
            // BarButtonItemLast
            // 
            this.BarButtonItemLast.Caption = "Last";
            this.BarButtonItemLast.Id = 4;
            this.BarButtonItemLast.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemLast.ImageOptions.SvgImage")));
            this.BarButtonItemLast.Name = "BarButtonItemLast";
            // 
            // BarButtonItemNew
            // 
            this.BarButtonItemNew.Caption = "New";
            this.BarButtonItemNew.Id = 5;
            this.BarButtonItemNew.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemNew.ImageOptions.SvgImage")));
            this.BarButtonItemNew.Name = "BarButtonItemNew";
            // 
            // BarButtonItemEdit
            // 
            this.BarButtonItemEdit.Caption = "Edit";
            this.BarButtonItemEdit.Id = 6;
            this.BarButtonItemEdit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemEdit.ImageOptions.SvgImage")));
            this.BarButtonItemEdit.Name = "BarButtonItemEdit";
            // 
            // BarButtonItemSave
            // 
            this.BarButtonItemSave.Caption = "Save";
            this.BarButtonItemSave.Id = 7;
            this.BarButtonItemSave.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemSave.ImageOptions.SvgImage")));
            this.BarButtonItemSave.Name = "BarButtonItemSave";
            // 
            // BarButtonItemCancel
            // 
            this.BarButtonItemCancel.Caption = "Cancel";
            this.BarButtonItemCancel.Id = 8;
            this.BarButtonItemCancel.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemCancel.ImageOptions.SvgImage")));
            this.BarButtonItemCancel.Name = "BarButtonItemCancel";
            // 
            // BarButtonItemDelete
            // 
            this.BarButtonItemDelete.Caption = "Delete";
            this.BarButtonItemDelete.Id = 9;
            this.BarButtonItemDelete.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemDelete.ImageOptions.SvgImage")));
            this.BarButtonItemDelete.Name = "BarButtonItemDelete";
            // 
            // BarButtonItemAddRow
            // 
            this.BarButtonItemAddRow.Caption = "Add Row";
            this.BarButtonItemAddRow.Id = 10;
            this.BarButtonItemAddRow.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemAddRow.ImageOptions.SvgImage")));
            this.BarButtonItemAddRow.Name = "BarButtonItemAddRow";
            this.BarButtonItemAddRow.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            // 
            // BarButtonItemPrint
            // 
            this.BarButtonItemPrint.Caption = "Print";
            this.BarButtonItemPrint.Id = 11;
            this.BarButtonItemPrint.Name = "BarButtonItemPrint";
            // 
            // tglStatus
            // 
            this.tglStatus.Caption = "Active";
            this.tglStatus.Id = 12;
            this.tglStatus.Name = "tglStatus";
            this.tglStatus.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            // 
            // BarButtonPrint
            // 
            this.BarButtonPrint.Caption = "Print";
            this.BarButtonPrint.Id = 14;
            this.BarButtonPrint.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonPrint.ImageOptions.SvgImage")));
            this.BarButtonPrint.Name = "BarButtonPrint";
            this.BarButtonPrint.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            // 
            // BarButtonPrintPreview
            // 
            this.BarButtonPrintPreview.Caption = "Print Preview";
            this.BarButtonPrintPreview.Id = 15;
            this.BarButtonPrintPreview.ImageOptions.SvgImage = global::ProManage.Properties.Resources.preview;
            this.BarButtonPrintPreview.Name = "BarButtonPrintPreview";
            this.BarButtonPrintPreview.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            // 
            // RibbonPageEstimate
            // 
            this.RibbonPageEstimate.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.RibbonPageGroupOperations,
            this.RibbonPageGroupGrid,
            this.RibbonPageGroupNavigation,
            this.ribbonPageGroup1});
            this.RibbonPageEstimate.Name = "RibbonPageEstimate";
            this.RibbonPageEstimate.Text = "Estimate";
            // 
            // RibbonPageGroupOperations
            // 
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemNew);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemEdit, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemSave, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemCancel, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemDelete, true);
            this.RibbonPageGroupOperations.Name = "RibbonPageGroupOperations";
            this.RibbonPageGroupOperations.Text = "Operations";
            // 
            // RibbonPageGroupGrid
            // 
            this.RibbonPageGroupGrid.ItemLinks.Add(this.BarButtonItemAddRow);
            this.RibbonPageGroupGrid.ItemLinks.Add(this.tglStatus);
            this.RibbonPageGroupGrid.Name = "RibbonPageGroupGrid";
            this.RibbonPageGroupGrid.Text = "Data Control";
            // 
            // RibbonPageGroupNavigation
            // 
            this.RibbonPageGroupNavigation.Alignment = DevExpress.XtraBars.Ribbon.RibbonPageGroupAlignment.Far;
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemFirst);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemPrevious, true);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemNext, true);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemLast, true);
            this.RibbonPageGroupNavigation.Name = "RibbonPageGroupNavigation";
            this.RibbonPageGroupNavigation.Text = "Navigation";
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.BarButtonPrintPreview);
            this.ribbonPageGroup1.ItemLinks.Add(this.BarButtonPrint);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            this.ribbonPageGroup1.Text = "Print";
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "Previous";
            this.barButtonItem1.Id = 2;
            this.barButtonItem1.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItem1.ImageOptions.SvgImage")));
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // MenuRibbon
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.RibbonControl);
            this.Name = "MenuRibbon";
            this.Size = new System.Drawing.Size(1019, 132);
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.Ribbon.RibbonControl RibbonControl;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemFirst;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrevious;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNext;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemLast;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNew;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemEdit;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemSave;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemCancel;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemDelete;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemAddRow;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrint;
        public DevExpress.XtraBars.BarToggleSwitchItem tglStatus;
        private DevExpress.XtraBars.BarButtonItem BarButtonPrint;
        private DevExpress.XtraBars.BarButtonItem BarButtonPrintPreview;
        private DevExpress.XtraBars.Ribbon.RibbonPage RibbonPageEstimate;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupOperations;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupGrid;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupNavigation;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        public DevExpress.XtraBars.BarButtonItem barButtonItem1;
    }
}
