using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.PermissionManagementForm
{
    #region Core Permission Classes

    /// <summary>
    /// Represents role-based permissions for a specific form
    /// </summary>
    public class RolePermission
    {
        /// <summary>
        /// Primary key for the permission record
        /// </summary>
        public int PermissionId { get; set; }

        /// <summary>
        /// Foreign key reference to the role
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Name of the form this permission applies to
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FormName { get; set; }

        /// <summary>
        /// Permission to read/view the form
        /// </summary>
        public bool ReadPermission { get; set; }

        /// <summary>
        /// Permission to create new records in the form
        /// </summary>
        public bool NewPermission { get; set; }

        /// <summary>
        /// Permission to edit existing records in the form
        /// </summary>
        public bool EditPermission { get; set; }

        /// <summary>
        /// Permission to delete records in the form
        /// </summary>
        public bool DeletePermission { get; set; }

        /// <summary>
        /// Permission to print from the form
        /// </summary>
        public bool PrintPermission { get; set; }

        /// <summary>
        /// Date when this permission was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public RolePermission()
        {
            CreatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// Represents user-specific permission overrides for a form
    /// NULL values indicate inheritance from role permissions
    /// </summary>
    public class UserPermission
    {
        /// <summary>
        /// Primary key for the user permission record
        /// </summary>
        public int UserPermissionId { get; set; }

        /// <summary>
        /// Foreign key reference to the user
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Name of the form this permission applies to
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FormName { get; set; }

        /// <summary>
        /// Permission to read/view the form (NULL = inherit from role)
        /// </summary>
        public bool? ReadPermission { get; set; }

        /// <summary>
        /// Permission to create new records (NULL = inherit from role)
        /// </summary>
        public bool? NewPermission { get; set; }

        /// <summary>
        /// Permission to edit existing records (NULL = inherit from role)
        /// </summary>
        public bool? EditPermission { get; set; }

        /// <summary>
        /// Permission to delete records (NULL = inherit from role)
        /// </summary>
        public bool? DeletePermission { get; set; }

        /// <summary>
        /// Permission to print from the form (NULL = inherit from role)
        /// </summary>
        public bool? PrintPermission { get; set; }

        /// <summary>
        /// Date when this permission was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public UserPermission()
        {
            CreatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// Represents global permissions for user management operations
    /// </summary>
    public class GlobalPermission
    {
        /// <summary>
        /// Primary key for the global permission record
        /// </summary>
        public int GlobalPermissionId { get; set; }

        /// <summary>
        /// Foreign key reference to the user
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Permission to create new users
        /// </summary>
        public bool CanCreateUsers { get; set; }

        /// <summary>
        /// Permission to edit existing users
        /// </summary>
        public bool CanEditUsers { get; set; }

        /// <summary>
        /// Permission to delete users
        /// </summary>
        public bool CanDeleteUsers { get; set; }

        /// <summary>
        /// Permission to print user reports
        /// </summary>
        public bool CanPrintUsers { get; set; }

        /// <summary>
        /// Date when this permission was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public GlobalPermission()
        {
            CreatedDate = DateTime.Now;
        }
    }

    #endregion

    #region Enums

    /// <summary>
    /// Types of permissions that can be checked
    /// </summary>
    public enum PermissionType
    {
        /// <summary>
        /// Permission to read/view data
        /// </summary>
        Read,

        /// <summary>
        /// Permission to create new records
        /// </summary>
        New,

        /// <summary>
        /// Permission to edit existing records
        /// </summary>
        Edit,

        /// <summary>
        /// Permission to delete records
        /// </summary>
        Delete,

        /// <summary>
        /// Permission to print reports
        /// </summary>
        Print
    }

    /// <summary>
    /// Types of global permissions for user management
    /// </summary>
    public enum GlobalPermissionType
    {
        /// <summary>
        /// Permission to create new users
        /// </summary>
        CanCreateUsers,

        /// <summary>
        /// Permission to edit existing users
        /// </summary>
        CanEditUsers,

        /// <summary>
        /// Permission to delete users
        /// </summary>
        CanDeleteUsers,

        /// <summary>
        /// Permission to print user reports
        /// </summary>
        CanPrintUsers
    }

    /// <summary>
    /// Source of an effective permission
    /// </summary>
    public enum PermissionSource
    {
        /// <summary>
        /// Permission comes from user's role
        /// </summary>
        Role,

        /// <summary>
        /// Permission is overridden by user-specific setting
        /// </summary>
        UserOverride
    }

    #endregion

    #region Composite Models for UI

    /// <summary>
    /// Represents the effective permission for a user on a specific form
    /// after resolving role permissions and user overrides
    /// </summary>
    public class EffectivePermission
    {
        /// <summary>
        /// Name of the form this permission applies to
        /// </summary>
        public string FormName { get; set; }

        /// <summary>
        /// Effective read permission
        /// </summary>
        public bool ReadPermission { get; set; }

        /// <summary>
        /// Effective new record permission
        /// </summary>
        public bool NewPermission { get; set; }

        /// <summary>
        /// Effective edit permission
        /// </summary>
        public bool EditPermission { get; set; }

        /// <summary>
        /// Effective delete permission
        /// </summary>
        public bool DeletePermission { get; set; }

        /// <summary>
    /// Effective print permission
    /// </summary>
    public bool PrintPermission { get; set; }

    /// <summary>
    /// Role-based read permission
    /// </summary>
    public bool RoleReadPermission { get; set; }

    /// <summary>
    /// Role-based new record permission
    /// </summary>
    public bool RoleNewPermission { get; set; }

    /// <summary>
    /// Role-based edit permission
    /// </summary>
    public bool RoleEditPermission { get; set; }

    /// <summary>
    /// Role-based delete permission
    /// </summary>
    public bool RoleDeletePermission { get; set; }

    /// <summary>
    /// Role-based print permission
    /// </summary>
    public bool RolePrintPermission { get; set; }

    /// <summary>
    /// User-specific read permission override (null means inherit from role)
    /// </summary>
    public bool? UserReadPermission { get; set; }

    /// <summary>
    /// User-specific new record permission override (null means inherit from role)
    /// </summary>
    public bool? UserNewPermission { get; set; }

    /// <summary>
    /// User-specific edit permission override (null means inherit from role)
    /// </summary>
    public bool? UserEditPermission { get; set; }

    /// <summary>
    /// User-specific delete permission override (null means inherit from role)
    /// </summary>
    public bool? UserDeletePermission { get; set; }

    /// <summary>
    /// User-specific print permission override (null means inherit from role)
    /// </summary>
    public bool? UserPrintPermission { get; set; }

    /// <summary>
    /// Source of this effective permission
    /// </summary>
    public PermissionSource Source { get; set; }
}

    /// <summary>
    /// Comprehensive user information with all permissions
    /// </summary>
    public class UserWithPermissions
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// User's full name
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// User's role ID
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// User's role name
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// List of effective permissions for all forms
        /// </summary>
        public List<EffectivePermission> FormPermissions { get; set; }

        /// <summary>
        /// Global permissions for user management
        /// </summary>
        public GlobalPermission GlobalPermissions { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public UserWithPermissions()
        {
            FormPermissions = new List<EffectivePermission>();
        }
    }

    #endregion

    #region Request Models

    /// <summary>
    /// Request model for creating a new role
    /// </summary>
    public class RoleCreateRequest
    {
        /// <summary>
        /// Name of the role to create
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RoleName { get; set; }

        /// <summary>
        /// Description of the role
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Whether the role is active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Request model for checking specific permissions
    /// </summary>
    public class PermissionRequest
    {
        /// <summary>
        /// User ID to check permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Form name to check permissions for
        /// </summary>
        [Required]
        public string FormName { get; set; }

        /// <summary>
        /// Type of permission to check
        /// </summary>
        public PermissionType PermissionType { get; set; }
    }

    /// <summary>
    /// Request model for checking global permissions
    /// </summary>
    public class GlobalPermissionRequest
    {
        /// <summary>
        /// User ID to check permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Type of global permission to check
        /// </summary>
        public GlobalPermissionType PermissionType { get; set; }
    }

    #endregion

    #region Form Configuration Models

    /// <summary>
    /// Configuration information for a form in the permission system
    /// </summary>
    public class FormConfiguration
    {
        /// <summary>
        /// Internal form name (class name)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FormName { get; set; }

        /// <summary>
        /// Display name for the form in UI
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Category for grouping forms
        /// </summary>
        [StringLength(50)]
        public string Category { get; set; }

        /// <summary>
        /// Whether this form is active in the permission system
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Sort order for display purposes
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public FormConfiguration()
        {
            IsActive = true;
            SortOrder = 0;
        }
    }

    /// <summary>
    /// Configuration information for a form category
    /// </summary>
    public class CategoryConfiguration
    {
        /// <summary>
        /// Internal category name
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CategoryName { get; set; }

        /// <summary>
        /// Display name for the category in UI
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Sort order for display purposes
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Whether this category is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public CategoryConfiguration()
        {
            IsActive = true;
            SortOrder = 0;
        }
    }

    /// <summary>
    /// Collection of form configurations with metadata
    /// </summary>
    public class FormsConfigurationCollection
    {
        /// <summary>
        /// List of form configurations
        /// </summary>
        public List<FormConfiguration> Forms { get; set; }

        /// <summary>
        /// List of category configurations
        /// </summary>
        public List<CategoryConfiguration> Categories { get; set; }

        /// <summary>
        /// When this collection was last updated
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public FormsConfigurationCollection()
        {
            Forms = new List<FormConfiguration>();
            Categories = new List<CategoryConfiguration>();
            LastUpdated = DateTime.Now;
        }
    }

    #endregion

    #region User Information Models

    /// <summary>
    /// Model for user information used in permission management
    /// </summary>
    public class UserInfo
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Full name of the user
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// User's role ID
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// User's role name
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// Whether the user is active
        /// </summary>
        public bool IsActive { get; set; }
    }

    #endregion

    #region Update Models

    /// <summary>
    /// Model for updating role permissions
    /// </summary>
    public class RolePermissionUpdate
    {
        /// <summary>
        /// Role ID to update permissions for
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Form name to update permissions for
        /// </summary>
        [Required]
        public string FormName { get; set; }

        /// <summary>
        /// Read permission value
        /// </summary>
        public bool ReadPermission { get; set; }

        /// <summary>
        /// New permission value
        /// </summary>
        public bool NewPermission { get; set; }

        /// <summary>
        /// Edit permission value
        /// </summary>
        public bool EditPermission { get; set; }

        /// <summary>
        /// Delete permission value
        /// </summary>
        public bool DeletePermission { get; set; }

        /// <summary>
        /// Print permission value
        /// </summary>
        public bool PrintPermission { get; set; }
    }

    /// <summary>
    /// Model for updating user permission overrides
    /// </summary>
    public class UserPermissionUpdate
    {
        /// <summary>
        /// User ID to update permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Form name to update permissions for
        /// </summary>
        [Required]
        public string FormName { get; set; }

        /// <summary>
        /// Read permission override (null = inherit from role)
        /// </summary>
        public bool? ReadPermission { get; set; }

        /// <summary>
        /// New permission override (null = inherit from role)
        /// </summary>
        public bool? NewPermission { get; set; }

        /// <summary>
        /// Edit permission override (null = inherit from role)
        /// </summary>
        public bool? EditPermission { get; set; }

        /// <summary>
        /// Delete permission override (null = inherit from role)
        /// </summary>
        public bool? DeletePermission { get; set; }

        /// <summary>
        /// Print permission override (null = inherit from role)
        /// </summary>
        public bool? PrintPermission { get; set; }
    }

    /// <summary>
    /// Model for updating global permissions
    /// </summary>
    public class GlobalPermissionUpdate
    {
        /// <summary>
        /// User ID to update global permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Permission to create new users
        /// </summary>
        public bool CanCreateUsers { get; set; }

        /// <summary>
        /// Permission to edit existing users
        /// </summary>
        public bool CanEditUsers { get; set; }

        /// <summary>
        /// Permission to delete users
        /// </summary>
        public bool CanDeleteUsers { get; set; }

        /// <summary>
        /// Permission to print user reports
        /// </summary>
        public bool CanPrintUsers { get; set; }
    }

    /// <summary>
    /// Helper class for user with role information
    /// </summary>
    public class UserWithRole
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Role ID assigned to the user
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Role name for display purposes
        /// </summary>
        public string RoleName { get; set; }
    }

    #endregion

    #region Summary and Statistics Models

    /// <summary>
    /// Summary statistics for the permission system
    /// </summary>
    public class PermissionSummary
    {
        /// <summary>
        /// Number of active roles in the system
        /// </summary>
        public int ActiveRoles { get; set; }

        /// <summary>
        /// Number of active users in the system
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// Number of forms configured in the system
        /// </summary>
        public int FormsInSystem { get; set; }

        /// <summary>
        /// Number of user permission overrides
        /// </summary>
        public int UserOverrides { get; set; }

        /// <summary>
        /// Number of users with global permissions
        /// </summary>
        public int UsersWithGlobalPermissions { get; set; }
    }

    /// <summary>
    /// Represents a complete permission set for a form (used by MenuRibbon UC)
    /// </summary>
    public class FormPermissionSet
    {
        /// <summary>
        /// Name of the form this permission set applies to
        /// </summary>
        public string FormName { get; set; }

        /// <summary>
        /// Whether the user can read/view the form
        /// </summary>
        public bool CanRead { get; set; }

        /// <summary>
        /// Whether the user can create new records
        /// </summary>
        public bool CanCreate { get; set; }

        /// <summary>
        /// Whether the user can edit existing records
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Whether the user can delete records
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Whether the user can print from the form
        /// </summary>
        public bool CanPrint { get; set; }

        // Alias properties for backward compatibility with tests
        /// <summary>
        /// Alias for CanRead (for test compatibility)
        /// </summary>
        public bool ReadPermission => CanRead;

        /// <summary>
        /// Alias for CanCreate (for test compatibility)
        /// </summary>
        public bool NewPermission => CanCreate;

        /// <summary>
        /// Alias for CanEdit (for test compatibility)
        /// </summary>
        public bool EditPermission => CanEdit;

        /// <summary>
        /// Alias for CanDelete (for test compatibility)
        /// </summary>
        public bool DeletePermission => CanDelete;

        /// <summary>
        /// Alias for CanPrint (for test compatibility)
        /// </summary>
        public bool PrintPermission => CanPrint;

        /// <summary>
        /// Constructor
        /// </summary>
        public FormPermissionSet()
        {
            FormName = "";
            CanRead = false;
            CanCreate = false;
            CanEdit = false;
            CanDelete = false;
            CanPrint = false;
        }
    }

    #endregion
}
